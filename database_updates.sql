-- Database updates for DERS system
-- Run these SQL commands to update the database structure

-- 1. Add employee_of_org_id column to applicants table
-- This field will appear if the employee is a public servant
-- It references the dakoii_org table for organization selection
ALTER TABLE applicants 
ADD COLUMN employee_of_org_id INT(11) UNSIGNED NULL DEFAULT NULL 
AFTER public_service_file_number;

-- Add foreign key constraint for employee_of_org_id
ALTER TABLE applicants 
ADD CONSTRAINT fk_applicants_employee_of_org_id 
FOREIGN KEY (employee_of_org_id) REFERENCES dakoii_org(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- 2. Add priority column to education_levels table
-- This field will be used to order education levels by priority
ALTER TABLE education_levels
ADD COLUMN priority INT UNSIGNED NOT NULL DEFAULT 0 
AFTER remarks;

-- Create index on priority column for better performance
CREATE INDEX idx_education_levels_priority ON education_levels(priority);

-- Optional: Update existing education levels with default priorities
-- You can run these if you want to set initial priorities for existing data
-- UPDATE education_levels SET priority = 1 WHERE name LIKE '%Primary%' OR name LIKE '%Elementary%';
-- UPDATE education_levels SET priority = 2 WHERE name LIKE '%Secondary%' OR name LIKE '%High School%';
-- UPDATE education_levels SET priority = 3 WHERE name LIKE '%Certificate%';
-- UPDATE education_levels SET priority = 4 WHERE name LIKE '%Diploma%';
-- UPDATE education_levels SET priority = 5 WHERE name LIKE '%Bachelor%' OR name LIKE '%Degree%';
-- UPDATE education_levels SET priority = 6 WHERE name LIKE '%Master%';
-- UPDATE education_levels SET priority = 7 WHERE name LIKE '%PhD%' OR name LIKE '%Doctorate%';

-- Add is_internal field to exercises table
ALTER TABLE `exercises` ADD `is_internal` TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'is internal advertisment or external advertisement' AFTER `advertisement_date`;

-- Update publish_date_from and publish_date_to fields to support datetime instead of just date
-- This allows storing both date and time for application deadlines
ALTER TABLE `exercises` MODIFY COLUMN `publish_date_from` DATETIME NOT NULL;
ALTER TABLE `exercises` MODIFY COLUMN `publish_date_to` DATETIME NOT NULL;
