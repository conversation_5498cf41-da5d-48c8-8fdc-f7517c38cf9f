<?php

namespace App\Controllers;

class PositionsController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = \Config\Services::session();
        // Remove all model initialization for UI development
    }

    public function positions_groups($exercise_id)
    {
        try {
            // Load models
            $exerciseModel = new \App\Models\ExerciseModel();
            $positionsGroupModel = new \App\Models\PositionsGroupModel();
            $positionsModel = new \App\Models\PositionsModel();

            // Get exercise data from database
            $exercise = $exerciseModel->find($exercise_id);
            if (!$exercise) {
                return redirect()->to('positions/positions_exercises')
                                ->with('error', 'Exercise not found');
            }

            // Get position groups for this exercise with parent group information
            $groups = $positionsGroupModel
                ->select('
                    positions_groups.*,
                    parent_group.group_name as parent_name
                ')
                ->join('positions_groups as parent_group', 'positions_groups.parent_id = parent_group.id', 'left')
                ->where('positions_groups.exercise_id', $exercise_id)
                ->orderBy('positions_groups.group_name', 'ASC')
                ->findAll();

            // Add position count for each group
            foreach ($groups as &$group) {
                $group['position_count'] = $positionsModel
                    ->where('position_group_id', $group['id'])
                    ->countAllResults();

                // Count positions without JD files (WO-JD)
                $group['wo_jd_count'] = $positionsModel
                    ->where('position_group_id', $group['id'])
                    ->groupStart()
                        ->where('jd_filepath', null)
                        ->orWhere('jd_filepath', '')
                    ->groupEnd()
                    ->countAllResults();
            }

            $data = [
                'title' => 'Position Groups',
                'menu' => 'positions',
                'exercise' => $exercise,
                'positions_groups' => $groups,
                'can_delete' => $exercise['status'] === 'draft'
            ];

            return view('positions/positions_group', $data);
        } catch (\Exception $e) {
            // Log the error and redirect with error message
            log_message('error', 'Error loading position groups: ' . $e->getMessage());

            return redirect()->to('positions/positions_exercises')
                            ->with('error', 'Unable to load position groups. Please try again later.');
        }
    }

    public function addPositionGroup()
    {
        $positionsGroupModel = new \App\Models\PositionsGroupModel();
        $exercise_id = $this->request->getPost('exercise_id');
        $group_name = $this->request->getPost('group_name');

        // Check if group name is unique within the exercise
        if (!$positionsGroupModel->isGroupNameUniqueInExercise($group_name, $exercise_id)) {
            session()->setFlashdata('error', 'A position group with this name already exists in this exercise');
            return redirect()->to('positions/positions_groups/' . $exercise_id);
        }

        $data = [
            'org_id' => session()->get('org_id'), // or get from form if needed
            'exercise_id' => $exercise_id,
            'parent_id' => $this->request->getPost('parent_id') ?: null,
            'group_name' => $group_name,
            'description' => $this->request->getPost('description'),
            'created_by' => session()->get('user_id'),
        ];
        if ($positionsGroupModel->insert($data)) {
            session()->setFlashdata('success', 'Position group added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add position group: ' . implode(' ', $positionsGroupModel->errors()));
        }
        return redirect()->to('positions/positions_groups/' . $exercise_id);
    }

    public function updatePositionGroup()
    {
        $positionsGroupModel = new \App\Models\PositionsGroupModel();
        $id = $this->request->getPost('id');
        $exercise_id = $this->request->getPost('exercise_id');
        $group_name = $this->request->getPost('group_name');

        // Check if group name is unique within the exercise (excluding current record)
        if (!$positionsGroupModel->isGroupNameUniqueInExercise($group_name, $exercise_id, $id)) {
            session()->setFlashdata('error', 'A position group with this name already exists in this exercise');
            return redirect()->to('positions/positions_groups/' . $exercise_id);
        }

        $data = [
            'parent_id' => $this->request->getPost('parent_id') ?: null,
            'group_name' => $group_name,
            'description' => $this->request->getPost('description'),
            'updated_by' => session()->get('user_id'),
        ];
        if ($positionsGroupModel->update($id, $data)) {
            session()->setFlashdata('success', 'Position group updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update position group: ' . implode(' ', $positionsGroupModel->errors()));
        }
        return redirect()->to('positions/positions_groups/' . $exercise_id);
    }

    public function deletePositionGroup($id)
    {
        $positionsGroupModel = new \App\Models\PositionsGroupModel();
        $exerciseModel = new \App\Models\ExerciseModel();

        $group = $positionsGroupModel->find($id);
        if (!$group) {
            session()->setFlashdata('error', 'Position group not found');
            return redirect()->to('positions/positions_exercises');
        }

        $exercise_id = $group['exercise_id'];

        // Check if exercise is in draft status before allowing deletion
        $exercise = $exerciseModel->find($exercise_id);
        if ($exercise && $exercise['status'] !== 'draft') {
            session()->setFlashdata('error', 'Cannot delete position group. Exercise status must be "draft" to allow position group deletion.');
            return redirect()->to('positions/positions_groups/' . $exercise_id);
        }

        if ($positionsGroupModel->delete($id)) {
            session()->setFlashdata('success', 'Position group deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete position group.');
        }
        return redirect()->to('positions/positions_groups/' . $exercise_id);
    }

    public function positions_exercises()
    {
        try {
            // Load models
            $exerciseModel = new \App\Models\ExerciseModel();
            $positionsGroupModel = new \App\Models\PositionsGroupModel();
            $positionsModel = new \App\Models\PositionsModel();

            // Get exercises filtered by organization
            $orgId = session()->get('org_id');
            log_message('info', 'PositionsController::positions_exercises - Organization ID from session: ' . ($orgId ?: 'null'));

            if ($orgId) {
                $exercises = $exerciseModel->getExercisesByOrgId($orgId);
                log_message('info', 'PositionsController::positions_exercises - Found ' . count($exercises) . ' exercises for org_id: ' . $orgId);
            } else {
                $exercises = $exerciseModel->orderBy('created_at', 'DESC')->findAll();
                log_message('info', 'PositionsController::positions_exercises - No org_id in session, showing all ' . count($exercises) . ' exercises');
            }

            // Add group and position counts for each exercise
            foreach ($exercises as &$exercise) {
                // Count groups for this exercise
                $exercise['group_count'] = $positionsGroupModel->where('exercise_id', $exercise['id'])->countAllResults();

                // Count positions for this exercise through groups
                $exercise['position_count'] = $positionsModel
                    ->join('positions_groups', 'positions.position_group_id = positions_groups.id')
                    ->where('positions_groups.exercise_id', $exercise['id'])
                    ->countAllResults();
            }

            return view('positions/positions_exercises', [
                'title' => 'Position Exercises',
                'menu' => 'positions',
                'exercises' => $exercises
            ]);
        } catch (\Exception $e) {
            // Log the error and show user-friendly message
            log_message('error', 'Error loading position exercises: ' . $e->getMessage());

            return view('positions/positions_exercises', [
                'title' => 'Position Exercises',
                'menu' => 'positions',
                'exercises' => [],
                'error' => 'Unable to load exercises. Please try again later.'
            ]);
        }
    }

    public function exercises_list()
    {
        try {
            // Load models
            $exerciseModel = new \App\Models\ExerciseModel();
            $positionsGroupModel = new \App\Models\PositionsGroupModel();
            $positionsModel = new \App\Models\PositionsModel();

            // Get exercises filtered by organization
            $orgId = session()->get('org_id');
            log_message('info', 'PositionsController::exercises_list - Organization ID from session: ' . ($orgId ?: 'null'));

            if ($orgId) {
                $exercises = $exerciseModel->getExercisesByOrgId($orgId);
                log_message('info', 'PositionsController::exercises_list - Found ' . count($exercises) . ' exercises for org_id: ' . $orgId);
            } else {
                $exercises = $exerciseModel->orderBy('created_at', 'DESC')->findAll();
                log_message('info', 'PositionsController::exercises_list - No org_id in session, showing all ' . count($exercises) . ' exercises');
            }

            // Add group and position counts for each exercise
            foreach ($exercises as &$exercise) {
                // Count groups for this exercise
                $exercise['group_count'] = $positionsGroupModel->where('exercise_id', $exercise['id'])->countAllResults();

                // Count positions for this exercise through groups
                $exercise['position_count'] = $positionsModel
                    ->join('positions_groups', 'positions.position_group_id = positions_groups.id')
                    ->where('positions_groups.exercise_id', $exercise['id'])
                    ->countAllResults();
            }

            return $this->response->setJSON(['data' => $exercises]);
        } catch (\Exception $e) {
            // Log the error and return empty data for DataTable
            log_message('error', 'Error loading exercises list: ' . $e->getMessage());

            return $this->response->setJSON([
                'data' => [],
                'error' => 'Unable to load exercises data'
            ]);
        }
    }

    public function view_positions($group_id)
    {
        try {
            // Load models
            $positionsGroupModel = new \App\Models\PositionsGroupModel();
            $positionsModel = new \App\Models\PositionsModel();
            $exerciseModel = new \App\Models\ExerciseModel();

            // Get group data from database with parent group information
            $group = $positionsGroupModel
                ->select('
                    positions_groups.*,
                    parent_group.group_name as parent_name
                ')
                ->join('positions_groups as parent_group', 'positions_groups.parent_id = parent_group.id', 'left')
                ->find($group_id);

            if (!$group) {
                return redirect()->to('positions/positions_exercises')
                                ->with('error', 'Position group not found');
            }

            // Get exercise information for breadcrumb
            $exercise = $exerciseModel->find($group['exercise_id']);

            // Get positions for this group
            $positions = $positionsModel
                ->where('position_group_id', $group_id)
                ->orderBy('designation', 'ASC')
                ->findAll();

            $data = [
                'title' => 'View Positions',
                'menu' => 'positions',
                'group_id' => $group_id,
                'group' => $group,
                'exercise' => $exercise,
                'positions' => $positions,
                'can_delete' => $exercise['status'] === 'draft'
            ];

            return view('positions/positions_view', $data);
        } catch (\Exception $e) {
            // Log the error and redirect with error message
            log_message('error', 'Error loading positions: ' . $e->getMessage());

            return redirect()->to('positions/positions_exercises')
                            ->with('error', 'Unable to load positions. Please try again later.');
        }
    }

    public function addPosition()
    {
        $positionsModel = new \App\Models\PositionsModel();
        $positionsGroupModel = new \App\Models\PositionsGroupModel();
        $exerciseModel = new \App\Models\ExerciseModel();

        $group_id = $this->request->getPost('position_group_id');

        // Check if exercise is in draft status before allowing position creation
        $group = $positionsGroupModel->find($group_id);
        if ($group) {
            $exercise = $exerciseModel->find($group['exercise_id']);
            if ($exercise && $exercise['status'] !== 'draft') {
                session()->setFlashdata('error', 'Cannot add position. Exercise status must be "draft" to allow position creation.');
                return redirect()->to('positions/view_positions/' . $group_id);
            }
        }

        $data = [
            'exercise_id' => $group['exercise_id'], // Add exercise_id from the position group
            'position_group_id' => $group_id,
            'org_id' => session()->get('org_id'),
            'position_reference' => $this->request->getPost('position_reference'),
            'designation' => $this->request->getPost('designation'),
            'classification' => $this->request->getPost('classification'),
            'award' => $this->request->getPost('award'),
            'location' => $this->request->getPost('location'),
            'annual_salary' => $this->request->getPost('annual_salary'),
            'status' => $this->request->getPost('status'),
            'qualifications' => $this->request->getPost('qualifications'),
            'knowledge' => $this->request->getPost('knowledge'),
            'skills_competencies' => $this->request->getPost('skills_competencies'),
            'job_experiences' => $this->request->getPost('job_experiences'),
            'remarks' => $this->request->getPost('remarks'),
            'created_by' => session()->get('user_id'),
            'updated_by' => session()->get('user_id'),
        ];
        // Handle JD file upload
        $file = $this->request->getFile('jd_file');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            $newName = $file->getRandomName();
            $file->move('uploads/job_descriptions', $newName);
            $data['jd_filepath'] = 'public/uploads/job_descriptions/' . $newName;
        }

        // Handle AI extracted text
        $extractedText = $this->request->getPost('jd_texts_extracted');
        if (!empty($extractedText)) {
            $data['jd_texts_extracted'] = $extractedText;
        }
        if ($positionsModel->insert($data)) {
            session()->setFlashdata('success', 'Position added successfully');
        } else {
            session()->setFlashdata('error', 'Failed to add position: ' . implode(' ', $positionsModel->errors()));
        }
        return redirect()->to('positions/view_positions/' . $group_id);
    }

    public function updatePosition()
    {
        $positionsModel = new \App\Models\PositionsModel();
        $id = $this->request->getPost('id');
        $group_id = null;
        $position = $positionsModel->find($id);
        if ($position) {
            $group_id = $position['position_group_id'];
        }
        $data = [
            'position_reference' => $this->request->getPost('position_reference'),
            'designation' => $this->request->getPost('designation'),
            'classification' => $this->request->getPost('classification'),
            'award' => $this->request->getPost('award'),
            'location' => $this->request->getPost('location'),
            'annual_salary' => $this->request->getPost('annual_salary'),
            'status' => $this->request->getPost('status'),
            'qualifications' => $this->request->getPost('qualifications'),
            'knowledge' => $this->request->getPost('knowledge'),
            'skills_competencies' => $this->request->getPost('skills_competencies'),
            'job_experiences' => $this->request->getPost('job_experiences'),
            'remarks' => $this->request->getPost('remarks'),
            'updated_by' => session()->get('user_id'),
        ];
        // Handle JD file upload
        $file = $this->request->getFile('jd_file');
        if ($file && $file->isValid() && !$file->hasMoved()) {
            $newName = $file->getRandomName();
            $file->move('uploads/job_descriptions', $newName);
            $data['jd_filepath'] = 'public/uploads/job_descriptions/' . $newName;
        }

        // Handle AI extracted text
        $extractedText = $this->request->getPost('jd_texts_extracted');
        if (!empty($extractedText)) {
            $data['jd_texts_extracted'] = $extractedText;
        }
        if ($positionsModel->update($id, $data)) {
            session()->setFlashdata('success', 'Position updated successfully');
        } else {
            session()->setFlashdata('error', 'Failed to update position: ' . implode(' ', $positionsModel->errors()));
        }
        return redirect()->to('positions/view_positions/' . $group_id);
    }

    public function deletePosition($id)
    {
        $positionsModel = new \App\Models\PositionsModel();
        $positionsGroupModel = new \App\Models\PositionsGroupModel();
        $exerciseModel = new \App\Models\ExerciseModel();

        $position = $positionsModel->find($id);
        $group_id = $position ? $position['position_group_id'] : null;

        if (!$position) {
            session()->setFlashdata('error', 'Position not found.');
            return redirect()->to('positions/positions_exercises');
        }

        // Get the exercise status to check if deletion is allowed
        $group = $positionsGroupModel->find($position['position_group_id']);
        if ($group) {
            $exercise = $exerciseModel->find($group['exercise_id']);
            if ($exercise && $exercise['status'] !== 'draft') {
                session()->setFlashdata('error', 'Cannot delete position. Exercise status must be "draft" to allow position deletion.');
                return redirect()->to('positions/view_positions/' . $group_id);
            }
        }

        if ($positionsModel->delete($id)) {
            session()->setFlashdata('success', 'Position deleted successfully');
        } else {
            session()->setFlashdata('error', 'Failed to delete position.');
        }
        return redirect()->to('positions/view_positions/' . $group_id);
    }

    public function downloadJD($id)
    {
        // Simulate file download for UI development
        return $this->response->setJSON(['success' => false, 'message' => 'File download not available in UI development mode']);
    }

    public function create()
    {
        $group_id = $this->request->getGet('group_id');
        return view('positions/positions_create', ['group_id' => $group_id]);
    }

    public function store()
    {
        // This is the POST handler for creating a position
        return $this->addPosition();
    }

    public function edit($id)
    {
        $positionsModel = new \App\Models\PositionsModel();
        $position = $positionsModel->find($id);
        if (!$position) {
            session()->setFlashdata('error', 'Position not found');
            return redirect()->back();
        }
        return view('positions/positions_edit', ['position' => $position]);
    }

    public function show($id)
    {
        $positionsModel = new \App\Models\PositionsModel();
        $position = $positionsModel->find($id);
        if (!$position) {
            session()->setFlashdata('error', 'Position not found');
            return redirect()->back();
        }
        return view('positions/positions_show', ['position' => $position]);
    }

    /**
     * Download CSV template for position import
     */
    public function downloadCsvTemplate($group_id)
    {
        try {
            // Verify group exists
            $positionsGroupModel = new \App\Models\PositionsGroupModel();
            $group = $positionsGroupModel->find($group_id);

            if (!$group) {
                return redirect()->back()->with('error', 'Position group not found');
            }

            // Create CSV headers
            $headers = [
                'position_reference',
                'designation',
                'classification',
                'award',
                'location',
                'annual_salary',
                'qualifications',
                'knowledge',
                'skills_competencies',
                'job_experiences',
                'remarks'
            ];

            // Sample data row
            $sampleData = [
                'POS-001',
                'Senior Software Engineer',
                'Professional',
                'Bachelor Degree',
                'Port Moresby',
                'K80,000 - K100,000',
                'Bachelor degree in Computer Science or related field. Minimum 5 years experience.',
                'Programming languages: PHP, JavaScript, Python. Database: MySQL, PostgreSQL.',
                'Problem solving, Team leadership, Project management, Communication skills.',
                'Minimum 5 years in software development. Experience with web applications.',
                'Position requires occasional travel.'
            ];

            // Set headers for file download
            $filename = 'positions_import_template_' . date('Y-m-d') . '.csv';

            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
            header('Expires: 0');

            // Create file pointer
            $output = fopen('php://output', 'w');

            // Write headers
            fputcsv($output, $headers);

            // Write sample data
            fputcsv($output, $sampleData);

            fclose($output);
            exit;

        } catch (\Exception $e) {
            log_message('error', 'Error generating CSV template: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error generating CSV template');
        }
    }

    /**
     * Import positions from CSV file
     */
    public function importCsv()
    {
        try {
            $positionGroupId = $this->request->getPost('position_group_id');
            $skipDuplicates = $this->request->getPost('skip_duplicates') ? true : false;

            // Verify group exists
            $positionsGroupModel = new \App\Models\PositionsGroupModel();
            $exerciseModel = new \App\Models\ExerciseModel();
            $group = $positionsGroupModel->find($positionGroupId);

            if (!$group) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Position group not found'
                ]);
            }

            // Check if exercise is in draft status before allowing CSV import
            $exercise = $exerciseModel->find($group['exercise_id']);
            if ($exercise && $exercise['status'] !== 'draft') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Cannot import positions. Exercise status must be "draft" to allow position import.'
                ]);
            }

            // Check if file was uploaded
            $file = $this->request->getFile('csv_file');
            if (!$file || !$file->isValid()) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Please select a valid CSV file'
                ]);
            }

            // Validate file type and size
            if ($file->getExtension() !== 'csv') {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Only CSV files are allowed'
                ]);
            }

            if ($file->getSize() > 5 * 1024 * 1024) { // 5MB limit
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'File size must be less than 5MB'
                ]);
            }

            // Read CSV file
            $csvData = array_map('str_getcsv', file($file->getTempName()));

            if (empty($csvData)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'CSV file is empty'
                ]);
            }

            // Get headers from first row
            $headers = array_shift($csvData);

            // Expected headers
            $expectedHeaders = [
                'position_reference', 'designation', 'classification', 'award',
                'location', 'annual_salary', 'qualifications', 'knowledge',
                'skills_competencies', 'job_experiences', 'remarks'
            ];

            // Validate headers
            $missingHeaders = array_diff($expectedHeaders, $headers);
            if (!empty($missingHeaders)) {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Missing required columns: ' . implode(', ', $missingHeaders)
                ]);
            }

            // Process data
            $positionsModel = new \App\Models\PositionsModel();
            $imported = 0;
            $skipped = 0;
            $errors = [];

            foreach ($csvData as $rowIndex => $row) {
                $rowNumber = $rowIndex + 2; // +2 because we removed header and arrays are 0-indexed

                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                // Create associative array
                $data = array_combine($headers, $row);

                // Add required fields
                $data['position_group_id'] = $positionGroupId;
                $data['org_id'] = $group['org_id'];
                $data['exercise_id'] = $group['exercise_id'];
                $data['status'] = 'active';
                $data['created_by'] = session()->get('user_id') ?? 1;
                $data['updated_by'] = session()->get('user_id') ?? 1;

                // Check for duplicates if skip_duplicates is enabled
                if ($skipDuplicates) {
                    $existing = $positionsModel
                        ->where('position_group_id', $positionGroupId)
                        ->where('position_reference', $data['position_reference'])
                        ->first();

                    if ($existing) {
                        $skipped++;
                        continue;
                    }
                }

                // Validate and insert
                if ($positionsModel->insert($data)) {
                    $imported++;
                } else {
                    $skipped++;
                    $validationErrors = $positionsModel->errors();
                    $errors[] = "Row {$rowNumber}: " . implode(', ', $validationErrors);
                }
            }

            return $this->response->setJSON([
                'success' => true,
                'message' => 'Import completed successfully',
                'imported' => $imported,
                'skipped' => $skipped,
                'errors' => $errors
            ]);

        } catch (\Exception $e) {
            log_message('error', 'Error importing CSV: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error processing CSV file: ' . $e->getMessage()
            ]);
        }
    }
}
